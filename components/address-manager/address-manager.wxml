<!-- 地址管理组件 -->
<view class="address-manager">
  <!-- 地址显示区域 -->
  <view class="address-display" wx:if="{{!showAddressList && !showAddressForm}}" bindtap="showAddressSelector">
    <view class="address-content">
      <view class="address-info" wx:if="{{selectedAddress}}">
        <text class="address-name">{{selectedAddress.name}} {{selectedAddress.phone}}</text>
        <text class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detail}}</text>
      </view>
      <text class="address-placeholder" wx:else>{{placeholder || '请选择配送地址'}}</text>
      <text class="address-arrow">></text>
    </view>
  </view>

  <!-- 地址列表弹窗 -->
  <view class="address-modal" wx:if="{{showAddressList}}">
    <view class="modal-mask" bindtap="hideAddressSelector" catchtouchmove="preventTouchMove"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送地址</text>
        <view class="modal-close" bindtap="hideAddressSelector">×</view>
      </view>
      <scroll-view scroll-y="true" class="address-list">
        <view class="address-item {{selectedAddress && selectedAddress.id === item.id ? 'selected' : ''}}"
              wx:for="{{savedAddresses}}"
              wx:key="id">
          <view class="address-item-content" bindtap="selectAddress" data-id="{{item.id}}">
            <view class="address-item-header">
              <text class="address-name">{{item.name}}</text>
              <text class="address-phone">{{item.phone}}</text>
              <view class="address-default" wx:if="{{item.isDefault}}">默认</view>
            </view>
            <text class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</text>
          </view>
          <view class="address-actions" catchtap="preventBubble">
            <view class="action-btn edit" bindtap="editAddressWithPlugin" data-id="{{item.id}}">编辑</view>
            <view class="action-btn delete" bindtap="deleteAddress" data-id="{{item.id}}">删除</view>
            <view class="action-btn default" wx:if="{{!item.isDefault}}" bindtap="setDefaultAddress" data-id="{{item.id}}">设为默认</view>
          </view>
        </view>

        <view class="empty-address" wx:if="{{savedAddresses.length === 0}}">
          <text class="empty-text">暂无保存的地址</text>
        </view>
      </scroll-view>

      <view class="address-footer">
        <view class="add-address-btn" bindtap="addAddressWithPlugin">
          <text class="add-icon">+</text>
          <text>新增地址</text>
        </view>
      </view>
    </view>
  </view>
</view>